defmodule RepobotWeb.WebhookControllerTest do
  use RepobotWeb.ConnCase, async: true
  use Repobot.Test.Fixtures

  import Mox

  alias Repobot.Events.Event
  alias Repobot.PullRequest
  alias Repobot.Repo
  alias Repobot.Accounts.Organization

  setup :verify_on_exit!

  # Setup signature verifier mock for all tests
  setup do
    Repobot.Test.SignatureVerifierMock
    |> stub(:verify_signature, fn _conn -> :ok end)

    :ok
  end

  describe "handle/2 for push events" do
    setup do
      user = create_user()
      %{user: user}
    end

    test "logs push events to the events table", %{conn: conn, user: user} do
      # Create a repository
      repo =
        create_repository(%{
          template: true,
          name: "test-repo",
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      # Create push event payload
      payload = %{
        "ref" => "refs/heads/main",
        "repository" => %{
          "full_name" => repo.full_name,
          "default_branch" => "main"
        },
        "commits" => [
          %{
            "added" => [],
            "modified" => ["some/path.ex"]
          }
        ]
      }

      # Count events before webhook
      event_count_before = length(Repo.all(Event))

      # Make the webhook request
      conn =
        conn
        |> put_req_header("content-type", "application/json")
        |> put_req_header("x-github-event", "push")
        |> post("/hooks", payload)

      assert json_response(conn, 200) == %{"status" => "ok"}

      # Verify that an event was logged
      events_after = Repo.all(Event)
      assert length(events_after) == event_count_before + 1

      # Get the latest event
      latest_event = Enum.max_by(events_after, & &1.inserted_at)
      assert latest_event.type == "github.push"
      assert latest_event.organization_id == user.default_organization_id
      assert latest_event.payload["repository"]["full_name"] == repo.full_name
    end

    test "handles push to template repository's default branch", %{conn: conn} do
      # Create a user and repositories using fixtures
      user = create_user()

      template_repo =
        create_repository(%{
          template: true,
          name: "template-repo",
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      target_repo =
        create_repository(%{
          name: "target-repo",
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      # Create source file using fixture
      source_file =
        create_source_file(%{
          name: "config.ex",
          target_path: "config/config.ex",
          content: "old content",
          source_repository_id: template_repo.id,
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      # Associate source file with both repos
      Repo.insert!(%Repobot.RepositorySourceFile{
        repository_id: template_repo.id,
        source_file_id: source_file.id
      })

      Repo.insert!(%Repobot.RepositorySourceFile{
        repository_id: target_repo.id,
        source_file_id: source_file.id
      })

      # Setup GitHub API mock expectations
      Repobot.Test.GitHubMock
      |> expect(:client, 2, fn owner, repo ->
        cond do
          owner == template_repo.owner and repo == template_repo.name -> :test_client
          owner == target_repo.owner and repo == target_repo.name -> :target_client
        end
      end)
      |> expect(:get_file_content, fn client, owner, repo, path, commit_sha ->
        if client == :test_client and owner == template_repo.owner and
             repo == template_repo.name and path == "config/config.ex" do
          {:ok, "new content", %{"sha" => "new-sha"}}
        else
          raise "Unexpected arguments in get_file_content mock: #{inspect({client, owner, repo, path, commit_sha})}"
        end
      end)

      # Setup Sync mock expectations
      Repobot.Test.SyncMock
      |> expect(:sync_changes, fn source_files,
                                  source_repo,
                                  target_repo_arg,
                                  :target_client,
                                  _opts ->
        # Check that our single source file is passed
        [source_file_arg] = source_files
        assert source_file_arg.id == source_file.id
        assert source_repo.id == template_repo.id
        assert target_repo_arg.id == target_repo.id

        # We don't need to validate the opts content, just make sure the function can handle the arg
        {:ok, "Files updated successfully"}
      end)

      # Count events before webhook
      event_count_before = length(Repo.all(Event))

      # Create push event payload
      payload = %{
        "ref" => "refs/heads/main",
        "repository" => %{
          "full_name" => template_repo.full_name,
          "default_branch" => "main"
        },
        "commits" => [
          %{
            "added" => [],
            "modified" => ["config/config.ex"]
          }
        ]
      }

      # Make the webhook request
      conn =
        conn
        |> put_req_header("content-type", "application/json")
        |> put_req_header("x-github-event", "push")
        |> post("/hooks", payload)

      assert json_response(conn, 200) == %{"status" => "ok"}

      # Verify events were logged (push + sync events)
      events_after = Repo.all(Event)
      assert length(events_after) >= event_count_before + 2

      # Get the events by type
      push_events = Enum.filter(events_after, &(&1.type == "github.push"))
      sync_events = Enum.filter(events_after, &(&1.type == "repobot.sync"))

      # Verify push event details
      latest_push_event = Enum.max_by(push_events, & &1.inserted_at)
      assert latest_push_event.organization_id == template_repo.organization_id
      assert latest_push_event.payload["repository"]["full_name"] == template_repo.full_name

      # Verify sync event details
      latest_sync_event = Enum.max_by(sync_events, & &1.inserted_at)
      assert latest_sync_event.organization_id == target_repo.organization_id
      assert latest_sync_event.payload["template_repository_id"] == template_repo.id
      assert latest_sync_event.payload["target_repository_id"] == target_repo.id
      assert latest_sync_event.payload["result"] == "ok"
    end

    test "handles push with multiple files in template repository's default branch", %{conn: conn} do
      # Create a user and repositories using fixtures
      user = create_user()

      template_repo =
        create_repository(%{
          template: true,
          name: "template-repo",
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      target_repo =
        create_repository(%{
          name: "target-repo",
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      # Create first source file
      source_file1 =
        create_source_file(%{
          name: "config.ex",
          target_path: "config/config.ex",
          content: "old content 1",
          source_repository_id: template_repo.id,
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      # Create second source file
      source_file2 =
        create_source_file(%{
          name: "runtime.ex",
          target_path: "config/runtime.ex",
          content: "old content 2",
          source_repository_id: template_repo.id,
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      # Associate source files with both repos
      Repo.insert!(%Repobot.RepositorySourceFile{
        repository_id: template_repo.id,
        source_file_id: source_file1.id
      })

      Repo.insert!(%Repobot.RepositorySourceFile{
        repository_id: target_repo.id,
        source_file_id: source_file1.id
      })

      Repo.insert!(%Repobot.RepositorySourceFile{
        repository_id: template_repo.id,
        source_file_id: source_file2.id
      })

      Repo.insert!(%Repobot.RepositorySourceFile{
        repository_id: target_repo.id,
        source_file_id: source_file2.id
      })

      # Setup GitHub API mock expectations
      Repobot.Test.GitHubMock
      |> expect(:client, 2, fn owner, repo ->
        cond do
          owner == template_repo.owner and repo == template_repo.name -> :test_client
          owner == target_repo.owner and repo == target_repo.name -> :target_client
        end
      end)
      |> expect(:get_file_content, fn client, owner, repo, path, commit_sha ->
        if client == :test_client and owner == template_repo.owner and
             repo == template_repo.name and path == "config/config.ex" do
          {:ok, "new content 1", %{"sha" => "new-sha-1"}}
        else
          raise "Unexpected arguments in get_file_content mock: #{inspect({client, owner, repo, path, commit_sha})}"
        end
      end)
      |> expect(:get_file_content, fn client, owner, repo, path, commit_sha ->
        if client == :test_client and owner == template_repo.owner and
             repo == template_repo.name and path == "config/runtime.ex" do
          {:ok, "new content 2", %{"sha" => "new-sha-2"}}
        else
          raise "Unexpected arguments in get_file_content mock: #{inspect({client, owner, repo, path, commit_sha})}"
        end
      end)

      # Setup Sync mock expectations - expect a single call to sync_changes with both files
      Repobot.Test.SyncMock
      |> expect(:sync_changes, fn source_files,
                                  source_repo,
                                  target_repo_arg,
                                  :target_client,
                                  _opts ->
        # Check that both source files are passed
        assert length(source_files) == 2
        assert Enum.any?(source_files, fn sf -> sf.id == source_file1.id end)
        assert Enum.any?(source_files, fn sf -> sf.id == source_file2.id end)
        assert source_repo.id == template_repo.id
        assert target_repo_arg.id == target_repo.id
        # We don't need to validate the opts content
        {:ok, "Files updated successfully"}
      end)

      # Count events before webhook
      event_count_before = length(Repo.all(Event))

      # Create push event payload with multiple files changed
      payload = %{
        "ref" => "refs/heads/main",
        "repository" => %{
          "full_name" => template_repo.full_name,
          "default_branch" => "main"
        },
        "commits" => [
          %{
            "added" => [],
            "modified" => ["config/config.ex", "config/runtime.ex"]
          }
        ]
      }

      # Make the webhook request
      conn =
        conn
        |> put_req_header("content-type", "application/json")
        |> put_req_header("x-github-event", "push")
        |> post("/hooks", payload)

      assert json_response(conn, 200) == %{"status" => "ok"}

      # Verify events were logged (push + sync events)
      events_after = Repo.all(Event)
      assert length(events_after) >= event_count_before + 2

      # Verify push event has both file paths
      push_events = Enum.filter(events_after, &(&1.type == "github.push"))
      latest_push_event = Enum.max_by(push_events, & &1.inserted_at)

      commit = List.first(latest_push_event.payload["commits"])
      modified_files = commit["modified"]
      assert "config/config.ex" in modified_files
      assert "config/runtime.ex" in modified_files
    end

    test "ignores push to non-default branch", %{conn: conn} do
      # Create user and repository using fixtures
      user = create_user()

      template_repo =
        create_repository(%{
          template: true,
          name: "template-repo",
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      payload = %{
        "ref" => "refs/heads/feature",
        "repository" => %{
          "full_name" => template_repo.full_name,
          "default_branch" => "main"
        },
        "commits" => [
          %{
            "modified" => ["config/config.ex"]
          }
        ]
      }

      conn =
        conn
        |> put_req_header("content-type", "application/json")
        |> put_req_header("x-github-event", "push")
        |> post("/hooks", payload)

      assert json_response(conn, 200) == %{"status" => "ok"}
    end

    test "handles push to non-template repository", %{conn: conn} do
      # Create user and repository using fixtures
      user = create_user()

      repo =
        create_repository(%{
          template: false,
          name: "normal-repo",
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      payload = %{
        "ref" => "refs/heads/main",
        "repository" => %{
          "full_name" => repo.full_name,
          "default_branch" => "main"
        },
        "commits" => [
          %{
            "modified" => ["config/config.ex"]
          }
        ]
      }

      conn =
        conn
        |> put_req_header("content-type", "application/json")
        |> put_req_header("x-github-event", "push")
        |> post("/hooks", payload)

      assert json_response(conn, 200) == %{"status" => "ok"}
    end

    test "handles push with no matching source files", %{conn: conn} do
      # Create user and repository using fixtures
      user = create_user()

      template_repo =
        create_repository(%{
          template: true,
          name: "template-repo",
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      payload = %{
        "ref" => "refs/heads/main",
        "repository" => %{
          "full_name" => template_repo.full_name,
          "default_branch" => "main"
        },
        "commits" => [
          %{
            "modified" => ["some/untracked/file.ex"]
          }
        ]
      }

      conn =
        conn
        |> put_req_header("content-type", "application/json")
        |> put_req_header("x-github-event", "push")
        |> post("/hooks", payload)

      assert json_response(conn, 200) == %{"status" => "ok"}
    end

    test "handles GitHub API error", %{conn: conn} do
      # Create user and repositories using fixtures
      user = create_user()

      template_repo =
        create_repository(%{
          template: true,
          name: "template-repo",
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      target_repo =
        create_repository(%{
          name: "target-repo",
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      # Create source file using fixture
      source_file =
        create_source_file(%{
          name: "config.ex",
          target_path: "config/config.ex",
          content: "old content",
          source_repository_id: template_repo.id,
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      # Associate source file with both repos
      Repo.insert!(%Repobot.RepositorySourceFile{
        repository_id: template_repo.id,
        source_file_id: source_file.id
      })

      Repo.insert!(%Repobot.RepositorySourceFile{
        repository_id: target_repo.id,
        source_file_id: source_file.id
      })

      # Setup GitHub API mock to simulate error
      Repobot.Test.GitHubMock
      |> expect(:client, fn owner, repo
                            when owner == template_repo.owner and repo == template_repo.name ->
        :test_client
      end)
      |> expect(:get_file_content, fn client, owner, repo, path, commit_sha ->
        if client == :test_client and owner == template_repo.owner and
             repo == template_repo.name and path == "config/config.ex" do
          {:error, "API error"}
        else
          raise "Unexpected arguments in get_file_content mock: #{inspect({client, owner, repo, path, commit_sha})}"
        end
      end)

      payload = %{
        "ref" => "refs/heads/main",
        "repository" => %{
          "full_name" => template_repo.full_name,
          "default_branch" => "main"
        },
        "commits" => [
          %{
            "modified" => ["config/config.ex"]
          }
        ]
      }

      conn =
        conn
        |> put_req_header("content-type", "application/json")
        |> put_req_header("x-github-event", "push")
        |> post("/hooks", payload)

      assert json_response(conn, 200) == %{"status" => "ok"}
    end

    test "handles invalid signature", %{conn: conn} do
      # Override the default stub to simulate invalid signature
      Repobot.Test.SignatureVerifierMock
      |> expect(:verify_signature, fn _conn -> {:error, "Invalid signature"} end)

      payload = %{
        "ref" => "refs/heads/main",
        "repository" => %{
          "full_name" => "some/repo",
          "default_branch" => "main"
        },
        "commits" => []
      }

      conn =
        conn
        |> put_req_header("content-type", "application/json")
        |> put_req_header("x-github-event", "push")
        |> post("/hooks", payload)

      assert response(conn, 400) == "Bad Request"
    end

    test "preserves commit structure when multiple files are changed in a single commit", %{
      conn: conn
    } do
      # Create a user and repositories using fixtures
      user = create_user()

      template_repo =
        create_repository(%{
          template: true,
          name: "template-repo",
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      target_repo =
        create_repository(%{
          name: "target-repo",
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      # Create multiple source files
      source_file1 =
        create_source_file(%{
          name: "config.ex",
          target_path: "config/config.ex",
          content: "old content 1",
          source_repository_id: template_repo.id,
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      source_file2 =
        create_source_file(%{
          name: "runtime.ex",
          target_path: "config/runtime.ex",
          content: "old content 2",
          source_repository_id: template_repo.id,
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      # Associate both source files with both repos
      Repo.insert!(%Repobot.RepositorySourceFile{
        repository_id: template_repo.id,
        source_file_id: source_file1.id
      })

      Repo.insert!(%Repobot.RepositorySourceFile{
        repository_id: target_repo.id,
        source_file_id: source_file1.id
      })

      Repo.insert!(%Repobot.RepositorySourceFile{
        repository_id: template_repo.id,
        source_file_id: source_file2.id
      })

      Repo.insert!(%Repobot.RepositorySourceFile{
        repository_id: target_repo.id,
        source_file_id: source_file2.id
      })

      # Setup GitHub API mock expectations
      Repobot.Test.GitHubMock
      |> expect(:client, 2, fn owner, repo ->
        cond do
          owner == template_repo.owner and repo == template_repo.name -> :test_client
          owner == target_repo.owner and repo == target_repo.name -> :target_client
        end
      end)
      |> expect(:get_file_content, fn
        :test_client, owner, repo, "config/config.ex", _commit_sha
        when owner == template_repo.owner and repo == template_repo.name ->
          {:ok, "new content 1", %{"sha" => "new-sha-1"}}
      end)
      |> expect(:get_file_content, fn
        :test_client, owner, repo, "config/runtime.ex", _commit_sha
        when owner == template_repo.owner and repo == template_repo.name ->
          {:ok, "new content 2", %{"sha" => "new-sha-2"}}
      end)

      # The critical part: Verify sync_changes is called with BOTH files in a SINGLE call
      # to preserve the original commit structure and message
      original_commit_message = "Update configuration files"
      commit_sha = "abc123"

      Repobot.Test.SyncMock
      |> expect(:sync_changes, fn files, source_repo, target_repo_arg, :target_client, opts ->
        # Verify syncing both files together
        assert length(files) == 2
        assert Enum.any?(files, fn sf -> sf.id == source_file1.id end)
        assert Enum.any?(files, fn sf -> sf.id == source_file2.id end)
        assert source_repo.id == template_repo.id
        assert target_repo_arg.id == target_repo.id

        # Verify using the original commit message
        assert Keyword.get(opts, :commit_message) == original_commit_message

        {:ok, "Files updated successfully"}
      end)

      # Create push event payload with a single commit modifying both files
      payload = %{
        "ref" => "refs/heads/main",
        "repository" => %{
          "full_name" => template_repo.full_name,
          "default_branch" => "main"
        },
        "commits" => [
          %{
            "id" => commit_sha,
            "message" => original_commit_message,
            "added" => [],
            "modified" => ["config/config.ex", "config/runtime.ex"]
          }
        ]
      }

      # Make the webhook request
      conn =
        conn
        |> put_req_header("content-type", "application/json")
        |> put_req_header("x-github-event", "push")
        |> post("/hooks", payload)

      assert json_response(conn, 200) == %{"status" => "ok"}
    end

    test "handles push to template repository with template files", %{conn: conn} do
      # Create a user and repositories using fixtures
      user = create_user()

      template_repo =
        create_repository(%{
          template: true,
          name: "template-repo",
          user_id: user.id,
          organization_id: user.default_organization_id,
          data: %{"name" => "template-repo", "full_name" => "owner/template-repo"}
        })

      target_repo =
        create_repository(%{
          name: "target-repo",
          user_id: user.id,
          organization_id: user.default_organization_id,
          data: %{"name" => "target-repo", "full_name" => "owner/target-repo"},
          settings: %{"greeting" => "Hello from target repo!"}
        })

      # Create a source file that is a template
      source_file =
        create_source_file(%{
          name: "config.ex",
          target_path: "config/config.ex",
          content: "# {{ name }} configuration\n\ngreeting = \"{{ settings.greeting }}\"",
          is_template: true,
          source_repository_id: template_repo.id,
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      # Associate source file with both repos
      Repo.insert!(%Repobot.RepositorySourceFile{
        repository_id: template_repo.id,
        source_file_id: source_file.id
      })

      Repo.insert!(%Repobot.RepositorySourceFile{
        repository_id: target_repo.id,
        source_file_id: source_file.id
      })

      # Setup GitHub API mock expectations
      Repobot.Test.GitHubMock
      |> expect(:client, 2, fn owner, repo ->
        cond do
          owner == template_repo.owner and repo == template_repo.name -> :test_client
          owner == target_repo.owner and repo == target_repo.name -> :target_client
        end
      end)
      |> expect(:get_file_content, fn client, owner, repo, path, commit_sha ->
        if client == :test_client and owner == template_repo.owner and
             repo == template_repo.name and path == "config/config.ex" do
          {:ok, "# {{ name }} configuration\n\ngreeting = \"{{ settings.greeting }}\"",
           %{"sha" => "new-sha"}}
        else
          raise "Unexpected arguments in get_file_content mock: #{inspect({client, owner, repo, path, commit_sha})}"
        end
      end)

      # Setup Sync mock to verify template rendering
      Repobot.Test.SyncMock
      |> expect(:sync_changes, fn source_files,
                                  source_repo,
                                  target_repo_arg,
                                  :target_client,
                                  _opts ->
        # Check that our source file is passed and is a template
        [source_file_arg] = source_files
        assert source_file_arg.id == source_file.id
        assert source_file_arg.is_template == true
        assert source_repo.id == template_repo.id
        assert target_repo_arg.id == target_repo.id

        # Let's bypass the mock and directly test rendering to verify it works
        # This simulates what would actually happen in the sync process
        {:ok, rendered_content} =
          Repobot.SourceFiles.render_template_for_repository(source_file_arg, target_repo_arg)

        # Verify the template was rendered with the target repo's variables
        assert rendered_content =~ "# target-repo configuration"
        assert rendered_content =~ "greeting = \"Hello from target repo!\""

        {:ok, "Files updated successfully"}
      end)

      # Create push event payload
      payload = %{
        "ref" => "refs/heads/main",
        "repository" => %{
          "full_name" => template_repo.full_name,
          "default_branch" => "main"
        },
        "commits" => [
          %{
            "added" => [],
            "modified" => ["config/config.ex"]
          }
        ]
      }

      # Make the webhook request
      conn =
        conn
        |> put_req_header("content-type", "application/json")
        |> put_req_header("x-github-event", "push")
        |> post("/hooks", payload)

      assert json_response(conn, 200) == %{"status" => "ok"}
    end
  end

  describe "handle/2 for pull request events" do
    test "logs pull_request events to the events table", %{conn: conn} do
      # Create a user and repository
      user = create_user()

      repo =
        create_repository(%{
          name: "test-repo",
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      # Create pull request event payload
      payload = %{
        "action" => "opened",
        "pull_request" => %{
          "number" => 123,
          "title" => "Test PR",
          "html_url" => "https://github.com/owner/repo/pull/123",
          "merged" => false
        },
        "repository" => %{
          "full_name" => repo.full_name
        }
      }

      # Count events before webhook
      event_count_before = length(Repo.all(Event))

      # Make the webhook request
      conn =
        conn
        |> put_req_header("content-type", "application/json")
        |> put_req_header("x-github-event", "pull_request")
        |> post("/hooks", payload)

      assert json_response(conn, 200) == %{"status" => "ok"}

      # Verify that an event was logged
      events_after = Repo.all(Event)
      assert length(events_after) == event_count_before + 1

      # Get the latest event
      latest_event = Enum.max_by(events_after, & &1.inserted_at)
      assert latest_event.type == "github.pull_request.opened"
      assert latest_event.organization_id == user.default_organization_id
      assert latest_event.payload["action"] == "opened"
      assert latest_event.payload["pull_request"]["number"] == 123
    end

    test "handles pull request event for non-existent repository", %{conn: conn} do
      payload = %{
        "action" => "closed",
        "pull_request" => %{
          "number" => 123,
          "title" => "Test PR",
          "html_url" => "https://github.com/owner/repo/pull/123",
          "merged" => true
        },
        "repository" => %{
          "full_name" => "non/existent"
        }
      }

      conn =
        conn
        |> put_req_header("content-type", "application/json")
        |> put_req_header("x-github-event", "pull_request")
        |> post("/hooks", payload)

      assert json_response(conn, 200) == %{"status" => "ok"}
    end

    test "handles pull request event with no status change needed", %{conn: conn} do
      # Create a user and repository
      user = create_user()

      repo =
        create_repository(%{
          name: "test-repo",
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      # Create a source file for the PR
      source_file =
        create_source_file(%{
          name: "test.ex",
          target_path: "test.ex",
          content: "test content",
          source_repository_id: repo.id,
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      # Create a pull request
      pull_request =
        create_pull_request(%{
          repository: repo.full_name,
          pull_request_number: 123,
          status: "open",
          source_file_id: source_file.id
        })

      # Create payload for a "synchronize" action (which shouldn't change status)
      payload = %{
        "action" => "synchronize",
        "pull_request" => %{
          "number" => pull_request.pull_request_number,
          "title" => "Test PR",
          "html_url" => "https://github.com/owner/repo/pull/123",
          "merged" => false
        },
        "repository" => %{
          "full_name" => repo.full_name
        }
      }

      conn =
        conn
        |> put_req_header("content-type", "application/json")
        |> put_req_header("x-github-event", "pull_request")
        |> post("/hooks", payload)

      assert json_response(conn, 200) == %{"status" => "ok"}

      # Verify pull request status hasn't changed
      updated_pr = Repo.get!(PullRequest, pull_request.id)
      assert updated_pr.status == "open"
    end

    test "handles all pull request status transitions" do
      # Create a user and repository
      user = create_user()

      repo =
        create_repository(%{
          name: "test-repo",
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      # Create a source file for the PRs
      source_file =
        create_source_file(%{
          name: "test.ex",
          target_path: "test.ex",
          content: "test content",
          source_repository_id: repo.id,
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      test_cases = [
        # {initial_status, action, merged, expected_status}
        {"open", "closed", true, "merged"},
        {"open", "closed", false, "closed"},
        {"closed", "reopened", false, "open"},
        {"merged", "reopened", true, "open"}
      ]

      for {initial_status, action, merged, expected_status} <- test_cases do
        # Create a pull request with initial status
        pull_request =
          create_pull_request(%{
            repository: repo.full_name,
            pull_request_number: :rand.uniform(1000),
            status: initial_status,
            source_file_id: source_file.id
          })

        # Create payload
        payload = %{
          "action" => action,
          "pull_request" => %{
            "number" => pull_request.pull_request_number,
            "title" => "Test PR",
            "html_url" =>
              "https://github.com/owner/repo/pull/#{pull_request.pull_request_number}",
            "merged" => merged
          },
          "repository" => %{
            "full_name" => repo.full_name
          }
        }

        conn =
          build_conn()
          |> put_req_header("content-type", "application/json")
          |> put_req_header("x-github-event", "pull_request")
          |> post("/hooks", payload)

        assert json_response(conn, 200) == %{"status" => "ok"}

        # Verify pull request status has changed by querying the database directly
        updated_pr = Repo.get!(PullRequest, pull_request.id)

        assert updated_pr.status == expected_status,
               "Expected PR status to change from #{initial_status} to #{expected_status} when action=#{action} and merged=#{merged}"
      end
    end
  end

  describe "repository creation events" do
    setup do
      user = create_user(%{login: "testuser"})
      org = user.default_organization
      %{user: user, org: org}
    end

    test "successfully creates repository in database and broadcasts event", %{
      conn: conn,
      org: org
    } do
      # Repository data from GitHub webhook
      repository_data = %{
        "name" => "new-repo",
        "full_name" => "#{org.name}/new-repo",
        "language" => "Elixir",
        "fork" => false,
        "private" => true,
        "description" => "A new repository",
        "default_branch" => "main",
        "owner" => %{
          "login" => org.name
        }
      }

      payload = %{
        "action" => "created",
        "repository" => repository_data
      }

      # Subscribe to PubSub to verify broadcast
      Phoenix.PubSub.subscribe(Repobot.PubSub, "repository_events")

      # Count repositories before webhook
      repo_count_before = length(Repo.all(Repobot.Repository))

      conn =
        conn
        |> put_req_header("content-type", "application/json")
        |> put_req_header("x-github-event", "repository")
        |> post("/hooks", payload)

      assert json_response(conn, 200) == %{"status" => "ok"}

      # Verify repository was created in database
      repos_after = Repo.all(Repobot.Repository)
      assert length(repos_after) == repo_count_before + 1

      # Find the created repository
      created_repo = Enum.find(repos_after, &(&1.full_name == "#{org.name}/new-repo"))
      assert created_repo != nil
      assert created_repo.name == "new-repo"
      assert created_repo.owner == org.name
      assert created_repo.language == "Elixir"
      assert created_repo.fork == false
      assert created_repo.private == true
      assert created_repo.organization_id == org.id
      assert created_repo.data == repository_data

      # Verify PubSub broadcast was sent
      assert_receive {:repository_created, broadcast_data}
      assert broadcast_data.name == "new-repo"
      assert broadcast_data.full_name == "#{org.name}/new-repo"
      assert broadcast_data.owner == org.name
      assert broadcast_data.private == true
      assert broadcast_data.description == "A new repository"
      assert broadcast_data.default_branch == "main"
    end

    test "handles organization not found error and still broadcasts event", %{conn: conn} do
      # Repository data with non-existent owner
      repository_data = %{
        "name" => "new-repo",
        "full_name" => "nonexistent/new-repo",
        "language" => "Elixir",
        "fork" => false,
        "private" => true,
        "description" => "A new repository",
        "default_branch" => "main",
        "owner" => %{
          "login" => "nonexistent"
        }
      }

      payload = %{
        "action" => "created",
        "repository" => repository_data
      }

      # Subscribe to PubSub to verify broadcast still happens
      Phoenix.PubSub.subscribe(Repobot.PubSub, "repository_events")

      # Count repositories before webhook
      repo_count_before = length(Repo.all(Repobot.Repository))

      conn =
        conn
        |> put_req_header("content-type", "application/json")
        |> put_req_header("x-github-event", "repository")
        |> post("/hooks", payload)

      assert json_response(conn, 200) == %{"status" => "ok"}

      # Verify no repository was created in database
      repos_after = Repo.all(Repobot.Repository)
      assert length(repos_after) == repo_count_before

      # Verify PubSub broadcast was still sent despite error
      assert_receive {:repository_created, broadcast_data}
      assert broadcast_data.name == "new-repo"
      assert broadcast_data.full_name == "nonexistent/new-repo"
      assert broadcast_data.owner == "nonexistent"
      assert broadcast_data.private == true
      assert broadcast_data.description == "A new repository"
      assert broadcast_data.default_branch == "main"
    end

    test "handles repository already exists and still broadcasts event", %{conn: conn, org: org} do
      # Create an existing repository
      existing_repo =
        create_repository(%{
          name: "existing-repo",
          full_name: "#{org.name}/existing-repo",
          organization_id: org.id
        })

      # Repository data for the same repository
      repository_data = %{
        "name" => "existing-repo",
        "full_name" => "#{org.name}/existing-repo",
        "language" => "JavaScript",
        "fork" => false,
        "private" => false,
        "description" => "Updated description",
        "default_branch" => "main",
        "owner" => %{
          "login" => org.name
        }
      }

      payload = %{
        "action" => "created",
        "repository" => repository_data
      }

      # Subscribe to PubSub to verify broadcast
      Phoenix.PubSub.subscribe(Repobot.PubSub, "repository_events")

      # Count repositories before webhook
      repo_count_before = length(Repo.all(Repobot.Repository))

      conn =
        conn
        |> put_req_header("content-type", "application/json")
        |> put_req_header("x-github-event", "repository")
        |> post("/hooks", payload)

      assert json_response(conn, 200) == %{"status" => "ok"}

      # Verify no new repository was created
      repos_after = Repo.all(Repobot.Repository)
      assert length(repos_after) == repo_count_before

      # Verify existing repository wasn't modified
      unchanged_repo = Repo.get!(Repobot.Repository, existing_repo.id)
      assert unchanged_repo.name == existing_repo.name
      assert unchanged_repo.full_name == existing_repo.full_name

      # Verify PubSub broadcast was sent
      assert_receive {:repository_created, broadcast_data}
      assert broadcast_data.name == "existing-repo"
      assert broadcast_data.full_name == "#{org.name}/existing-repo"
      assert broadcast_data.owner == org.name
      assert broadcast_data.private == false
      assert broadcast_data.description == "Updated description"
      assert broadcast_data.default_branch == "main"
    end

    test "handles database creation failure and still broadcasts event", %{conn: conn, org: org} do
      # Repository data with invalid attributes to trigger changeset error
      repository_data = %{
        # Invalid name to trigger validation error
        "name" => nil,
        "full_name" => "#{org.name}/invalid-repo",
        "language" => "Elixir",
        "fork" => false,
        "private" => true,
        "description" => "A repository with invalid data",
        "default_branch" => "main",
        "owner" => %{
          "login" => org.name
        }
      }

      payload = %{
        "action" => "created",
        "repository" => repository_data
      }

      # Subscribe to PubSub to verify broadcast still happens
      Phoenix.PubSub.subscribe(Repobot.PubSub, "repository_events")

      # Count repositories before webhook
      repo_count_before = length(Repo.all(Repobot.Repository))

      conn =
        conn
        |> put_req_header("content-type", "application/json")
        |> put_req_header("x-github-event", "repository")
        |> post("/hooks", payload)

      assert json_response(conn, 200) == %{"status" => "ok"}

      # Verify no repository was created in database due to validation error
      repos_after = Repo.all(Repobot.Repository)
      assert length(repos_after) == repo_count_before

      # Verify PubSub broadcast was still sent despite database error
      assert_receive {:repository_created, broadcast_data}
      assert broadcast_data.name == nil
      assert broadcast_data.full_name == "#{org.name}/invalid-repo"
      assert broadcast_data.owner == org.name
      assert broadcast_data.private == true
      assert broadcast_data.description == "A repository with invalid data"
      assert broadcast_data.default_branch == "main"
    end
  end

  describe "installation events" do
    setup do
      # Create a test organization
      user = create_user(%{login: "testuser"})
      org = user.default_organization
      %{user: user, org: org}
    end

    test "updates organization installation_id when installation is created", %{
      conn: conn,
      org: org
    } do
      # Simulate GitHub installation webhook payload
      payload = %{
        "action" => "created",
        "installation" => %{
          "id" => 123_456,
          "account" => %{
            "login" => org.name,
            "type" => "User"
          }
        }
      }

      conn =
        conn
        |> put_req_header("content-type", "application/json")
        |> put_req_header("x-github-event", "installation")
        |> post("/hooks", payload)

      assert response(conn, 200)

      # Verify organization was updated
      updated_org = Repo.get(Organization, org.id)
      assert updated_org.installation_id == 123_456
    end

    test "removes organization installation_id when installation is deleted", %{
      conn: conn,
      org: org
    } do
      # First set an installation_id
      {:ok, org} = Repo.update(Organization.changeset(org, %{installation_id: 123_456}))
      assert org.installation_id == 123_456

      # Simulate GitHub installation webhook payload
      payload = %{
        "action" => "deleted",
        "installation" => %{
          "id" => 123_456,
          "account" => %{
            "login" => org.name,
            "type" => "User"
          }
        }
      }

      conn =
        conn
        |> put_req_header("content-type", "application/json")
        |> put_req_header("x-github-event", "installation")
        |> post("/hooks", payload)

      assert response(conn, 200)

      # Verify installation_id was removed
      updated_org = Repo.get(Organization, org.id)
      assert is_nil(updated_org.installation_id)
    end

    test "ignores installation events for unknown organizations", %{conn: conn} do
      # Simulate GitHub installation webhook payload for unknown org
      payload = %{
        "action" => "created",
        "installation" => %{
          "id" => 123_456,
          "account" => %{
            "login" => "unknown-org",
            "type" => "User"
          }
        }
      }

      conn =
        conn
        |> put_req_header("content-type", "application/json")
        |> put_req_header("x-github-event", "installation")
        |> post("/hooks", payload)

      assert response(conn, 200)
    end
  end
end
